import { TelegramChannelListener } from './TelegramChannelListener';
import promptSync from 'prompt-sync';
import OpenAI from 'openai';
import { NewsItem, NewsAnalysisResult, NewsAnalysisResponse } from '../types';
import TelegramBot from 'node-telegram-bot-api';
import dayjs from 'dayjs';

export default class AnalyzeNews {
  private telegramService;
  private openai: OpenAI;
  private bot: TelegramBot;

  constructor() {
    this.telegramService = new TelegramChannelListener(
      Number(process.env.TELEGRAM_APP_API_ID!),
      process.env.TELEGRAM_APP_HASH!,
    );
    this.bot = new TelegramBot(process.env.TELEGRAM_BOT_ID!, { polling: true });
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY!,
    });
  }

  async init() {
    const prompt = promptSync();

    await this.telegramService.connect({
      phoneNumber: async () => prompt('📱 Enter phone number: '),
      phoneCode: async () => prompt('📩 Enter the code you received: '),
      onError: (err: any) => console.error(err),
    });

    this.setupCommands();
  }

  private setupCommands() {
    this.bot.onText(/\/news (\w+) (.+)/, async (msg, match) => {
      const chatId = msg.chat.id;
      const period = match?.[1];
      const channelUsername = match?.[2];

      if (!period || !channelUsername) {
        this.bot.sendMessage(chatId, 'Usage: /news 1d @channelname or /news 1w @channelname');
        return;
      }

      try {
        await this.handleNewsCommand(chatId, period, channelUsername);
      } catch (error) {
        this.bot.sendMessage(chatId, `Error: ${error}`);
      }
    });

    this.bot.onText(/\/start/, (msg) => {
      const chatId = msg.chat.id;
      this.bot.sendMessage(
        chatId,
        'Welcome! Use /news 1d @channelname for daily news or /news 1w @channelname for weekly news analysis.',
      );
    });
  }

  private async handleNewsCommand(chatId: number, period: string, channelUsername: string) {
    try {
      this.bot.sendMessage(chatId, `Fetching messages from ${channelUsername} for the last ${period}...`);

      // Calculate date range based on period
      const { fromDate, toDate } = this.calculateDateRange(period);

      const channelMessages = await this.telegramService.getMessagesByDateRange(
        channelUsername,
        fromDate,
        toDate,
        500, // Fetch up to 500 messages
      );

      const newsMessages = channelMessages.filter((msg) => msg.message.includes(' 🔴'));

      if (newsMessages.length === 0) {
        this.bot.sendMessage(chatId, `No news found in ${channelUsername} for the last ${period}.`);
        return;
      }

      const newsItems: NewsItem[] = newsMessages.map((msg) => ({
        timestamp: msg.date.toISOString(),
        message: msg.message,
        source: 'telegram_channel',
      }));

      this.bot.sendMessage(chatId, `Analyzing ${newsItems.length} news items from the last ${period}...`);

      const result = await this.analyzeNews(newsItems);

      const message = this.formatAnalysisMessage(result.analysis, result.summary);
      this.bot.sendMessage(chatId, message, { parse_mode: 'HTML' });
    } catch (error) {
      console.error('Error in handleNewsCommand:', error);
      this.bot.sendMessage(chatId, `Error fetching messages from ${channelUsername}: ${error}`);
    }
  }

  private formatAnalysisMessage(analysis: any, summary: string): string {
    return `
<b>📊 News Analysis Summary</b>

<b>🏛️ Equities:</b> ${analysis.Equities}

<b>💱 FX:</b> ${analysis.FX}

<b>🛢️ Commodities:</b> ${analysis.Commodities}

<b>📈 Rates:</b> ${analysis.Rates}

<b>🌍 Geopolitics:</b> ${analysis.Geopolitics}

<b>🎯 Key Theme:</b> ${analysis.KeyTheme}

<b>📝 Summary:</b>
${summary}
    `.trim();
  }

  private calculateDateRange(period: string): { fromDate: Date; toDate: Date } {
    const now = dayjs();
    let fromDate: dayjs.Dayjs;

    switch (period) {
      case '1d':
        fromDate = now.subtract(1, 'day');
        break;
      case '1w':
        fromDate = now.subtract(1, 'week');
        break;
      default:
        throw new Error('Invalid period. Use 1d or 1w.');
    }

    return {
      fromDate: fromDate.toDate(),
      toDate: now.toDate(),
    };
  }

  async analyzeNews(newsData: NewsItem[]): Promise<NewsAnalysisResult> {
    const systemPrompt = 'You are a macroeconomic and financial markets expert analyst.';

    const userPrompt = `News data (JSON):
${JSON.stringify(newsData, null, 2)}

Nhiệm vụ:
1. Phân loại từng tin tức vào các nhóm sau:
   - Chính sách tiền tệ & lãi suất
   - Dữ liệu kinh tế vĩ mô
   - Thị trường chứng khoán
   - Tiền tệ (FX)
   - Hàng hóa
   - Chính trị & địa chính trị

2. Đánh giá tác động của từng tin tức (Tích cực / Tiêu cực / Trung lập) và xác định các thị trường liên quan (Chứng khoán, FX, Hàng hóa, Lãi suất).

3. Tạo bản tóm tắt trong đúng cấu trúc JSON sau:
   {
     "Equities": "...",
     "FX": "...",
     "Commodities": "...",
     "Rates": "...",
     "Geopolitics": "...",
     "KeyTheme": "..."
   }

4. Viết thêm một đoạn tóm tắt ngắn (3–5 câu) để đọc nhanh nhưng phải đủ ý, nếu cần thiết thì tăng lên 5-10 câu.`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        temperature: 0.7,
        max_tokens: 2000,
      });

      const content = response.choices[0]?.message?.content;

      if (!content) {
        throw new Error('No content received from OpenAI');
      }

      const jsonMatch = content.match(/\{[\s\S]*?\}/);
      if (!jsonMatch) {
        throw new Error('No JSON structure found in response');
      }

      const analysis: NewsAnalysisResponse = JSON.parse(jsonMatch[0]);

      const summaryMatch = content.split(jsonMatch[0])[1]?.trim();
      const summary = summaryMatch || 'No summary available';

      return {
        analysis,
        summary,
      };
    } catch (error) {
      throw new Error(`Failed to analyze news: ${error}`);
    }
  }
}
