import { TelegramChannelListener } from './TelegramChannelListener';
import promptSync from 'prompt-sync';
import OpenAI from 'openai';
import { NewsItem, NewsAnalysisResult, NewsAnalysisResponse } from '../types';
import TelegramBot from 'node-telegram-bot-api';
import dayjs from 'dayjs';

export default class AnalyzeNews {
  private telegramService;
  private openai: OpenAI;
  private bot: TelegramBot;

  constructor() {
    this.telegramService = new TelegramChannelListener(
      Number(process.env.TELEGRAM_APP_API_ID!),
      process.env.TELEGRAM_APP_HASH!,
    );
    this.bot = new TelegramBot(process.env.TELEGRAM_BOT_ID!, { polling: true });
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY!,
    });
  }

  async init() {
    const prompt = promptSync();

    await this.telegramService.connect({
      phoneNumber: async () => prompt('📱 Enter phone number: '),
      phoneCode: async () => prompt('📩 Enter the code you received: '),
      onError: (err: any) => console.error(err),
    });

    this.setupCommands();
  }

  private setupCommands() {
    this.bot.onText(/\/news (\w+) (.+)/, async (msg, match) => {
      const chatId = msg.chat.id;
      const period = match?.[1];
      console.log(match, 'match');

      if (!period) {
        this.bot.sendMessage(chatId, 'Usage: /news 1d or /news 1w');
        return;
      }

      try {
        await this.handleNewsCommand(chatId, period);
      } catch (error) {
        this.bot.sendMessage(chatId, `Error: ${error}`);
      }
    });

    this.bot.onText(/\/start/, (msg) => {
      const chatId = msg.chat.id;
      this.bot.sendMessage(chatId, 'Welcome! Use /news 1d  for daily news or /news 1w  for weekly news analysis.');
    });
  }

  private async handleNewsCommand(chatId: number, period: string, channelUsername = 'vnwallstreet') {
    try {
      this.bot.sendMessage(chatId, `Fetching messages...`);

      const { fromDate, toDate } = this.calculateDateRange(period);

      const channelMessages = await this.telegramService.getMessagesByDateRange(
        channelUsername,
        fromDate,
        toDate,
        500, // Fetch up to 500 messages
      );

      const newsMessages = channelMessages;

      if (newsMessages.length === 0) {
        this.bot.sendMessage(chatId, `No news found in ${channelUsername} for the last ${period}.`);
        return;
      }

      const newsItems: NewsItem[] = newsMessages.map((msg) => ({
        timestamp: msg.date.toISOString(),
        message: msg.message,
        source: 'telegram_channel',
      }));

      this.bot.sendMessage(chatId, `Analyzing ${newsItems.length} news items from the last ${period}...`);

      const result = await this.analyzeNews(newsItems);

      const message = this.formatAnalysisMessage(result.analysis, result.summary);
      this.bot.sendMessage(chatId, message, { parse_mode: 'HTML' });
    } catch (error) {
      console.error('Error in handleNewsCommand:', error);
      this.bot.sendMessage(chatId, `Error fetching messages from ${channelUsername}: ${error}`);
    }
  }

  private formatAnalysisMessage(analysis: any, summary: string): string {
    return `
<b>📊 News Analysis Summary</b>

<b>🏛️ Equities:</b> ${analysis.Equities}

<b>💱 FX:</b> ${analysis.FX}

<b>🛢️ Commodities:</b> ${analysis.Commodities}

<b>₿ Crypto:</b> ${analysis.Crypto}

<b>📈 Rates:</b> ${analysis.Rates}

<b>🌍 Geopolitics:</b> ${analysis.Geopolitics}

<b>🎯 Key Theme:</b> ${analysis.KeyTheme}

<b>📝 Summary:</b>
${summary}
    `.trim();
  }

  private calculateDateRange(period: string): { fromDate: Date; toDate: Date } {
    const now = dayjs();
    let fromDate: dayjs.Dayjs;

    switch (period) {
      case '1d':
        fromDate = now.subtract(1, 'day');
        break;
      case '1w':
        fromDate = now.subtract(1, 'week');
        break;
      default:
        throw new Error('Invalid period. Use 1d or 1w.');
    }

    return {
      fromDate: fromDate.toDate(),
      toDate: now.toDate(),
    };
  }

  private optimizeNewsData(newsData: NewsItem[]): string {
    const uniqueNews = this.removeDuplicateNews(newsData);

    const groupedNews = this.groupAndSummarizeNews(uniqueNews);

    const optimizedNews = groupedNews.map((item, index) => ({
      id: index + 1,
      date: dayjs(item.timestamp).format('YYYY-MM-DD HH:mm'),
      content: this.extractImportantContent(item.message),
    }));

    optimizedNews.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    const maxItems = Math.min(40, optimizedNews.length); // Adaptive limit
    const limitedNews = optimizedNews.slice(0, maxItems);

    return JSON.stringify(limitedNews, null, 2);
  }

  private removeDuplicateNews(newsData: NewsItem[]): NewsItem[] {
    const seen = new Set<string>();
    const unique: NewsItem[] = [];

    for (const item of newsData) {
      const simplified = item.message
        .toLowerCase()
        .replace(/[^\w\s]/g, '')
        .replace(/\s+/g, ' ')
        .trim();

      const key = simplified.substring(0, 100);

      if (!seen.has(key)) {
        seen.add(key);
        unique.push(item);
      }
    }

    return unique;
  }

  private extractKeyContent(message: string): string {
    let cleaned = message
      .replace(/🔴/g, '')
      .replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/gu, '') // Remove other emojis
      .replace(/https?:\/\/[^\s]+/g, '[LINK]')
      .replace(/\s+/g, ' ')
      .trim();

    if (cleaned.length > 300) {
      cleaned = cleaned.substring(0, 300) + '...';
    }

    return cleaned;
  }

  private estimateTokens(text: string): number {
    return Math.ceil(text.length / 3);
  }

  private groupAndSummarizeNews(newsData: NewsItem[]): NewsItem[] {
    const groups: { [key: string]: NewsItem[] } = {};

    const topicKeywords = {
      monetary: [
        // Vietnamese terms
        'lãi suất',
        'ngân hàng',
        'chính sách tiền tệ',
        'sbv',
        'ngân hàng nhà nước',
        'tăng lãi suất',
        'giảm lãi suất',
        'tái cấp vốn',
        'dự trữ bắt buộc',
        'tỷ lệ dự trữ',
        'thanh khoản',
        'cung tiền',
        // International terms
        'fed',
        'interest rate',
        'monetary policy',
        'central bank',
        'rate hike',
        'rate cut',
      ],
      economic: [
        // Vietnamese terms
        'gdp',
        'tăng trưởng',
        'kinh tế',
        'lạm phát',
        'cpi',
        'pmi',
        'xuất khẩu',
        'nhập khẩu',
        'thương mại',
        'sản xuất công nghiệp',
        'bán lẻ',
        'tiêu dùng',
        'đầu tư',
        'fdi',
        'thất nghiệp',
        'việc làm',
        // International terms
        'inflation',
        'employment',
        'trade',
        'export',
        'import',
        'manufacturing',
        'retail',
      ],
      equity: [
        // Vietnamese terms
        'chứng khoán',
        'cổ phiếu',
        'vn-index',
        'hnx',
        'upcom',
        'thị trường chứng khoán',
        'niêm yết',
        'giao dịch',
        'khối lượng',
        'thanh khoản',
        'tăng điểm',
        'giảm điểm',
        'blue chip',
        // International terms
        'stock',
        'equity',
        'market',
        'index',
        'dow',
        'nasdaq',
        's&p',
        'trading',
        'shares',
      ],
      fx: [
        // Vietnamese terms
        'tỷ giá',
        'usd/vnd',
        'ngoại tệ',
        'đồng việt nam',
        'tỷ giá trung tâm',
        'biến động tỷ giá',
        'dự trữ ngoại hối',
        'can thiệp tỷ giá',
        'phá giá',
        'tăng giá',
        // International terms
        'dollar',
        'euro',
        'currency',
        'forex',
        'exchange rate',
        'usd',
        'eur',
        'jpy',
        'cny',
      ],
      commodity: [
        // Vietnamese terms
        'dầu thô',
        'xăng dầu',
        'vàng',
        'hàng hóa',
        'nông sản',
        'gạo',
        'cà phê',
        'cao su',
        'thép',
        'than',
        'điện',
        'gas',
        'giá xăng',
        'giá vàng',
        // International terms
        'oil',
        'crude',
        'gold',
        'commodity',
        'metal',
        'copper',
        'silver',
        'wheat',
        'corn',
      ],
      geopolitical: [
        // Vietnamese terms
        'chiến tranh',
        'xung đột',
        'thương chiến',
        'trừng phạt',
        'ngoại giao',
        'quan hệ quốc tế',
        'trung quốc',
        'mỹ',
        'nga',
        'ukraine',
        'biển đông',
        'asean',
        // International terms
        'war',
        'conflict',
        'trade war',
        'sanction',
        'china',
        'usa',
        'russia',
        'ukraine',
        'nato',
      ],
      crypto: [
        // Major cryptocurrencies
        'bitcoin',
        'btc',
        'ethereum',
        'eth',
        'crypto',
        'cryptocurrency',
        'tiền điện tử',
        'tiền mã hóa',
        // Vietnamese crypto terms
        'bitcoin',
        'ethereum',
        'tiền ảo',
        'tài sản số',
        'blockchain',
        'defi',
        'nft',
        // Exchanges and platforms
        'binance',
        'coinbase',
        'ftx',
        'kraken',
        'bitfinex',
        'huobi',
        'okx',
        // Major altcoins
        'altcoin',
        'bnb',
        'ada',
        'sol',
        'dot',
        'avax',
        'matic',
        'link',
        'uni',
        'xrp',
        'ltc',
        'bch',
        'etc',
        'xlm',
        'trx',
        'eos',
        'atom',
        'algo',
        // Stablecoins
        'usdt',
        'usdc',
        'busd',
        'dai',
        'stablecoin',
        'tether',
        // Crypto market terms
        'mining',
        'staking',
        'yield farming',
        'liquidity',
        'dex',
        'cex',
        'wallet',
        'cold storage',
        'hot wallet',
        'private key',
        'seed phrase',
        'smart contract',
        'gas fee',
        'transaction fee',
        'confirmation',
        'market cap',
        'volume',
        'dominance',
        'fear and greed',
        'rsi',
        'macd',
        'support',
        'resistance',
        'breakout',
        'dump',
        'pump',
        'whale',
        'hodl',
        'fomo',
        'fud',
        'ath',
        'atl',
        'bull market',
        'bear market',
        // Vietnamese crypto slang
        'lên sàn',
        'xuống sàn',
        'pump',
        'dump',
        'hold',
        'all in',
      ],
    };

    // Classify news into groups
    for (const item of newsData) {
      const content = item.message.toLowerCase();
      let assigned = false;

      for (const [topic, keywords] of Object.entries(topicKeywords)) {
        if (keywords.some((keyword) => content.includes(keyword))) {
          if (!groups[topic]) groups[topic] = [];
          groups[topic].push(item);
          assigned = true;
          break;
        }
      }

      if (!assigned) {
        if (!groups['other']) groups['other'] = [];
        groups['other'].push(item);
      }
    }

    const summarized: NewsItem[] = [];
    for (const [topic, items] of Object.entries(groups)) {
      const sorted = items.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
      const topItems = sorted.slice(0, topic === 'other' ? 2 : 4);
      summarized.push(...topItems);
    }

    return summarized;
  }

  private extractImportantContent(message: string): string {
    const importantPatterns = [
      // Percentages
      /\d+\.?\d*%/g,
      // Vietnamese currency (VND)
      /\d+[\d,]*\.?\d*\s*(vnd|đồng|triệu|tỷ)/gi,
      // Dollar amounts
      /\$\d+[\d,]*\.?\d*/g,
      // Basis points
      /\d+\.?\d*\s*(basis points|bps|điểm cơ bản)/gi,
      // Dates
      /\d{1,2}\/\d{1,2}\/\d{4}/g, // DD/MM/YYYY
      /\d{4}-\d{2}-\d{2}/g, // YYYY-MM-DD
      // Vietnamese directional changes
      /(tăng|giảm|tăng lên|giảm xuống|lên|xuống)\s*\d+/gi,
      // English directional changes
      /(increase|decrease|rise|fall|up|down)\s+\d+/gi,
      // Vietnamese market terms with numbers
      /(vn-index|hnx-index)\s*\d+/gi,
      // Exchange rates
      /(usd\/vnd|tỷ giá)\s*\d+/gi,
      // Crypto prices and market cap
      /(btc|bitcoin|eth|ethereum)\s*\$?\d+[\d,]*\.?\d*/gi,
      // Crypto percentage changes
      /(btc|eth|bitcoin|ethereum|crypto)\s*(tăng|giảm|up|down)\s*\d+\.?\d*%/gi,
      // Market cap patterns
      /market cap\s*\$?\d+[\d,]*\.?\d*[bmk]?/gi,
    ];

    let extracted = message;
    const importantInfo: string[] = [];

    // Extract important numerical data
    for (const pattern of importantPatterns) {
      const matches = extracted.match(pattern);
      if (matches) {
        importantInfo.push(...matches);
      }
    }

    // Extract Vietnamese-specific important terms
    const vietnameseImportantTerms = [
      /ngân hàng nhà nước/gi,
      /chính phủ/gi,
      /bộ tài chính/gi,
      /thủ tướng/gi,
      /quốc hội/gi,
      /fed/gi,
      /ecb/gi,
    ];

    for (const pattern of vietnameseImportantTerms) {
      const matches = extracted.match(pattern);
      if (matches) {
        importantInfo.push(...matches);
      }
    }

    extracted = this.extractKeyContent(message);

    if (importantInfo.length > 0) {
      const uniqueInfo = [...new Set(importantInfo)].join(', ');
      extracted = `[Dữ liệu quan trọng: ${uniqueInfo}] ${extracted}`;
    }

    return extracted;
  }

  async analyzeNews(newsData: NewsItem[]): Promise<NewsAnalysisResult> {
    const optimizedData = this.optimizeNewsData(newsData);
    const estimatedTokens = this.estimateTokens(optimizedData);

    console.log(`📊 Optimization Results:`);
    console.log(`- Original news items: ${newsData.length}`);
    console.log(`- Optimized items: ${JSON.parse(optimizedData).length}`);
    console.log(`- Estimated tokens: ${estimatedTokens}`);

    const systemPrompt = `You are a macroeconomic and financial markets expert analyst specializing in Vietnamese and global markets. Analyze news efficiently and provide concise insights in Vietnamese.`;

    const userPrompt = `Phân tích dữ liệu tin tức tài chính và đánh giá tác động thị trường:

${optimizedData}

Nhiệm vụ:
1. Phân loại tin tức theo: Chính sách tiền tệ, Dữ liệu kinh tế, Chứng khoán, Ngoại hối, Hàng hóa, Tiền điện tử, Địa chính trị
2. Đánh giá tác động (Tích cực/Tiêu cực/Trung tính) cho từng thị trường
3. Trả về định dạng JSON:
{
  "Equities": "Tóm tắt ngắn gọn tác động thị trường chứng khoán",
  "FX": "Tóm tắt ngắn gọn tác động thị trường ngoại hối",
  "Commodities": "Tóm tắt ngắn gọn tác động thị trường hàng hóa",
  "Crypto": "Tóm tắt ngắn gọn tác động thị trường tiền điện tử",
  "Rates": "Tóm tắt ngắn gọn tác động lãi suất/trái phiếu",
  "Geopolitics": "Tóm tắt ngắn gọn tình hình địa chính trị",
  "KeyTheme": "Chủ đề chính của giai đoạn này"
}

4. Sau đó viết tóm tắt tổng quan thị trường ngắn gọn và dự đoán market move.`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        temperature: 0.7,
        max_tokens: 2000,
      });

      const content = response.choices[0]?.message?.content;

      if (!content) {
        throw new Error('No content received from OpenAI');
      }

      const jsonMatch = content.match(/\{[\s\S]*?\}/);
      if (!jsonMatch) {
        throw new Error('No JSON structure found in response');
      }

      const analysis: NewsAnalysisResponse = JSON.parse(jsonMatch[0]);

      const summaryMatch = content.split(jsonMatch[0])[1]?.trim();
      const summary = summaryMatch || 'No summary available';

      return {
        analysis,
        summary,
      };
    } catch (error) {
      throw new Error(`Failed to analyze news: ${error}`);
    }
  }
}
