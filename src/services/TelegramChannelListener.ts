import Logger from '../libs/Logger';
import { TelegramClient } from 'telegram';
import { StringSession } from 'telegram/sessions';

type LoginHandlers = {
  phoneNumber: () => Promise<string>;
  phoneCode: () => Promise<string>;
  onError: (err: unknown) => void;
};

export class TelegramChannelListener {
  private client: TelegramClient;
  private session: StringSession;

  constructor(apiId: number, apiHash: string, sessionString = '') {
    this.session = new StringSession(sessionString);
    this.client = new TelegramClient(this.session, apiId, apiHash, {
      connectionRetries: 2,
    });
  }

  public async connect(config: LoginHandlers): Promise<void> {
    try {
      await this.client.start({
        phoneNumber: config.phoneNumber,
        phoneCode: config.phoneCode,
        onError: config.onError,
      });

      const me = await this.client.getMe();
      Logger.info('Connected as user:', me);
      Logger.info('Save this session string:');
      Logger.info(this.client.session.save());
    } catch (error) {
      Logger.error('Connection error:', error);
    }
  }

  public listenChannel(callback: (msg: string, update: any) => void): void {
    this.client.addEventHandler((update: any) => {
      if (update.className === 'UpdateNewChannelMessage') {
        Logger.info('Update from', update);
        const msg = update.message.message;
        callback(msg, update);
      }
    });
  }
}
