import Logger from '../libs/Logger';
import { TelegramClient } from 'telegram';
import { StringSession } from 'telegram/sessions';
import { Api } from 'telegram';

type LoginHandlers = {
  phoneNumber: () => Promise<string>;
  phoneCode: () => Promise<string>;
  onError: (err: unknown) => void;
};

type MessageHistoryOptions = {
  channelUsername?: string;
  channelId?: string;
  limit?: number;
  offsetDate?: Date;
  offsetId?: number;
  maxId?: number;
  minId?: number;
  addOffset?: number;
  hash?: bigint;
};

type ChannelMessage = {
  id: number;
  message: string;
  date: Date;
  fromId?: string;
  views?: number;
  forwards?: number;
  replies?: number;
};

export class TelegramChannelListener {
  private client: TelegramClient;
  private session: StringSession;

  constructor(apiId: number, apiHash: string, sessionString = '') {
    this.session = new StringSession(sessionString);
    this.client = new TelegramClient(this.session, apiId, apiHash, {
      connectionRetries: 2,
    });
  }

  public async connect(config: LoginHandlers): Promise<void> {
    try {
      await this.client.start({
        phoneNumber: config.phoneNumber,
        phoneCode: config.phoneCode,
        onError: config.onError,
      });

      const me = await this.client.getMe();
      Logger.info('Connected as user:', me);
      Logger.info('Save this session string:');
      Logger.info(this.client.session.save());
    } catch (error) {
      Logger.error('Connection error:', error);
    }
  }

  public listenChannel(callback: (msg: string, update: any) => void): void {
    this.client.addEventHandler((update: any) => {
      if (update.className === 'UpdateNewChannelMessage') {
        Logger.info('Update from', update);
        const msg = update.message.message;
        callback(msg, update);
      }
    });
  }

  /**
   * Get message history from a Telegram channel
   * @param options Configuration for message retrieval
   * @returns Array of channel messages
   */
  public async getChannelHistory(options: MessageHistoryOptions): Promise<ChannelMessage[]> {
    try {
      // Resolve channel entity
      let channel;
      if (options.channelUsername) {
        channel = await this.client.getEntity(options.channelUsername);
      } else if (options.channelId) {
        channel = await this.client.getEntity(options.channelId);
      } else {
        throw new Error('Either channelUsername or channelId must be provided');
      }

      // Get messages from the channel
      const result = await this.client.invoke(
        new Api.messages.GetHistory({
          peer: channel,
          limit: options.limit || 100,
          offsetDate: options.offsetDate ? Math.floor(options.offsetDate.getTime() / 1000) : 0,
          offsetId: options.offsetId || 0,
          maxId: options.maxId || 0,
          minId: options.minId || 0,
          addOffset: options.addOffset || 0,
          hash: options.hash || (0 as any),
        }),
      );

      // Process and format messages
      const messages: ChannelMessage[] = [];

      if ('messages' in result) {
        for (const msg of result.messages) {
          if (msg.className === 'Message' && msg.message) {
            messages.push({
              id: msg.id,
              message: msg.message,
              date: new Date(msg.date * 1000),
              fromId: msg.fromId?.toString(),
              views: msg.views,
              forwards: msg.forwards,
              replies: msg.replies?.replies,
            });
          }
        }
      }

      Logger.info(`Retrieved ${messages.length} messages from channel`);
      return messages;
    } catch (error) {
      Logger.error('Error getting channel history:', error);
      throw error;
    }
  }

  /**
   * Get recent messages from a channel (last N messages)
   * @param channelUsername Channel username (e.g., '@channelname')
   * @param limit Number of messages to retrieve (default: 50)
   * @returns Array of recent messages
   */
  public async getRecentMessages(channelUsername: string, limit: number = 50): Promise<ChannelMessage[]> {
    return this.getChannelHistory({
      channelUsername,
      limit,
    });
  }

  /**
   * Get messages from a specific time period
   * @param channelUsername Channel username
   * @param fromDate Start date
   * @param toDate End date (optional)
   * @param limit Maximum number of messages (default: 100)
   * @returns Array of messages from the time period
   */
  public async getMessagesByDateRange(
    channelUsername: string,
    fromDate: Date,
    toDate?: Date,
    limit: number = 100,
  ): Promise<ChannelMessage[]> {
    const messages = await this.getChannelHistory({
      channelUsername,
      limit,
      offsetDate: toDate,
    });

    // Filter messages by date range
    return messages.filter((msg) => {
      const msgDate = msg.date;
      const isAfterFrom = msgDate >= fromDate;
      const isBeforeTo = toDate ? msgDate <= toDate : true;
      return isAfterFrom && isBeforeTo;
    });
  }

  /**
   * Search for messages containing specific text
   * @param channelUsername Channel username
   * @param searchText Text to search for
   * @param limit Maximum number of messages to check (default: 200)
   * @returns Array of messages containing the search text
   */
  public async searchMessages(
    channelUsername: string,
    searchText: string,
    limit: number = 200,
  ): Promise<ChannelMessage[]> {
    const messages = await this.getChannelHistory({
      channelUsername,
      limit,
    });

    return messages.filter((msg) => msg.message.toLowerCase().includes(searchText.toLowerCase()));
  }
}
